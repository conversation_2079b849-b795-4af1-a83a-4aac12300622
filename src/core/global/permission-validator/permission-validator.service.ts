import { Injectable } from '@nestjs/common';
import { isArray, isEqual, isMatch, some } from 'lodash';
import { match } from 'ts-pattern';

import { Permissions } from '@core/db/entities/permissions';
import { Users } from '@core/db/entities/users';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';

import { ApproveStatus } from '@domain/v1/documents/documents.v1.constant';
import { PermissionsType } from '@domain/v1/permissions/permissions.v1.constant';
import { PermissionMetadata } from '@domain/v1/permissions/permissions.v1.type';
import { RoleName } from '@domain/v1/roles/roles.v1.constant';

import {
  permissionOptions,
  PermissionValidatorError,
} from './permission-validator.constants';
import { PermissionValidatorParams } from './permission-validator.type';

@Injectable()
export class PermissionValidatorService {
  validatePermission(
    params: PermissionValidatorParams,
  ): Res<null, typeof PermissionValidatorError.PermissionDenied> {
    const isValid = this.isValidPermission(params);
    if (!isValid) {
      return Err(PermissionValidatorError.PermissionDenied);
    }
    return Ok(null);
  }

  isValidPermission(params: PermissionValidatorParams): boolean {
    const { user, requiredPermissions, condition = 'AND' } = params;
    const userPermissions = this._getUserPermissionsWithSuperAdminCheck(user);
    const isSuperAdmin = isEqual(userPermissions, true);

    if (isSuperAdmin) return true;
    if (!isArray(userPermissions)) return false;

    if (condition === 'AND') {
      return requiredPermissions.every((required) =>
        this.checkSinglePermission(required, userPermissions),
      );
    }

    return requiredPermissions.some((required) =>
      this.checkSinglePermission(required, userPermissions),
    );
  }

  checkSinglePermission(
    required: PermissionMetadata,
    userPermissions: Permissions[],
  ): boolean {
    if (required.type === PermissionsType.Read) {
      // For type read: if the user has any permission on the item, they are considered to also have read permission.
      // Ex. if the user has the permission type "create" for the item "template", they are also considered to have "read" permission for "template".
      return some(userPermissions, { item: required.item });
    }

    const matchPattern = {
      type: required.type,
      item: required.item,
      approveStatus: required.approveStatus ?? null,
      department: required.department ?? null,
      area: required.area ?? null,
    };
    return some(userPermissions, (up) => isMatch(up, matchPattern));
  }

  getUserPermissions(user: Users): Permissions[] {
    const permissions: Permissions[] = [];

    user.userRoles.forEach((userRole) => {
      userRole.role.rolePermissions.forEach((rolePermission) => {
        permissions.push(rolePermission.permission);
      });
    });

    user.userPermissions.forEach((userPermission) => {
      permissions.push(userPermission.permission);
    });

    return permissions;
  }

  private _getUserPermissionsWithSuperAdminCheck(
    user: Users,
  ): Permissions[] | boolean {
    const isSuperAdmin = user.userRoles.find(
      (userRole) => userRole.role.name === RoleName.SuperAdmin,
    );

    if (isSuperAdmin) return true; // Super Admin has all permissions
    return this.getUserPermissions(user);
  }

  canDeleteTemplate(params: {
    user: Users;
    documentStatus: string;
    createdBy: {
      id: number;
    };
    department: {
      id: number;
    };
  }): boolean {
    const { createdBy, department, user } = params;

    const isOwner = user.id === createdBy.id;
    const isApproved = params.documentStatus === ApproveStatus.Approved;
    const isOwnDepartment = user.department.id === department.id;

    const requiredPermissions = match([isApproved, isOwnDepartment])
      .with([true, true], () => [
        permissionOptions.deleteTemplateApprovedOwnDept,
        permissionOptions.deleteTemplateApprovedAllDept,
      ])
      .with([true, false], () => [
        permissionOptions.deleteTemplateApprovedAllDept,
      ])
      .with([false, true], () => [
        permissionOptions.deleteTemplateNotApprovedOwnDept,
        permissionOptions.deleteTemplateNotApprovedAllDept,
      ])
      .with([false, false], () => [
        permissionOptions.deleteTemplateNotApprovedAllDept,
      ])
      .exhaustive();

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }

  canCreateTemplate(params: {
    user: Users;
    createdBy?: {
      id: number;
    };
    area: {
      id: number;
    };
  }): boolean {
    const { createdBy, area, user } = params;

    const isOwner = user.id === createdBy?.id;
    const isOwnArea = user.department.responsibilityArea.id === area.id;

    const requiredPermissions = match(isOwnArea)
      .with(true, () => [
        permissionOptions.createTemplateOwnArea,
        permissionOptions.createTemplateAllArea,
      ])
      .with(false, () => [permissionOptions.createTemplateAllArea])
      .exhaustive();

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }

  canEditTemplate(params: {
    user: Users;
    documentStatus: string;
    createdBy: {
      id: number;
    };
    department: {
      id: number;
    };
  }): boolean {
    const { createdBy, department, user } = params;

    const isOwner = user.id === createdBy.id;
    const isApproved = params.documentStatus === ApproveStatus.Approved;
    const isOwnDepartment = user.department.id === department.id;

    const requiredPermissions = match([isApproved, isOwnDepartment])
      .with([true, true], () => [
        permissionOptions.editTemplateApprovedOwnDept,
        permissionOptions.editTemplateApprovedAllDept,
      ])
      .with([true, false], () => [
        permissionOptions.editTemplateApprovedAllDept,
      ])
      .otherwise(() => []);

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }

  canDeleteReport(params: {
    user: Users;
    documentStatus: string;
    createdBy: {
      id: number;
    };
    department: {
      id: number;
    };
  }): boolean {
    const { createdBy, department, user } = params;

    const isOwner = user.id === createdBy.id;
    const isApproved = params.documentStatus === ApproveStatus.Approved;
    const isOwnDepartment = user.department.id === department.id;

    const requiredPermissions = match([isApproved, isOwnDepartment])
      .with([true, true], () => [
        permissionOptions.deleteReportApprovedOwnDept,
        permissionOptions.deleteReportApprovedAllDept,
      ])
      .with([true, false], () => [
        permissionOptions.deleteReportApprovedAllDept,
      ])
      .with([false, true], () => [
        permissionOptions.deleteReportNotApprovedOwnDept,
        permissionOptions.deleteReportNotApprovedAllDept,
      ])
      .with([false, false], () => [
        permissionOptions.deleteReportNotApprovedAllDept,
      ])
      .exhaustive();

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }

  canCreateReport(params: {
    user: Users;
    createdBy: {
      id: number;
    };
    area: {
      id: number;
    };
  }): boolean {
    const { createdBy, area, user } = params;

    const isOwner = user.id === createdBy.id;
    const isOwnArea = user.department.id === area.id;

    const requiredPermissions = match(isOwnArea)
      .with(true, () => [
        permissionOptions.createReportOwnArea,
        permissionOptions.createReportAllArea,
      ])
      .with(false, () => [permissionOptions.createReportAllArea])
      .exhaustive();

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }

  canEditReport(params: {
    user: Users;
    documentStatus: string;
    createdBy: {
      id: number;
    };
    department: {
      id: number;
    };
  }): boolean {
    const { createdBy, department, user } = params;

    const isOwner = user.id === createdBy.id;
    const isApproved = params.documentStatus === ApproveStatus.Approved;
    const isOwnDepartment = user.department.id === department.id;

    const requiredPermissions = match([isApproved, isOwnDepartment])
      .with([true, true], () => [
        permissionOptions.editReportApprovedOwnDept,
        permissionOptions.editReportOwnDept,
        permissionOptions.editReportAllDept,
      ])
      .otherwise(() => [permissionOptions.editReportAllDept]);

    return (
      isOwner ||
      this.isValidPermission({
        user,
        requiredPermissions,
        condition: 'OR',
      })
    );
  }
}

import { Command, CommandRunner, Option } from 'nest-commander';

import { DocumentType } from '@domain/v1/documents/documents.v1.constant';

import { reportsMetadataSeed } from '../../data/reports-metadata-seed';
import { ReportsCliRepo } from '../reports.cli.repo';

interface CommandOptions {
  amount: number;
  skipValidation: boolean;
  verbose: boolean;
}

@Command({
  name: 'reports:seed',
  description: 'Create comprehensive seed data for reports system testing',
})
export class ReportsCliSeed extends CommandRunner {
  constructor(private repo: ReportsCliRepo) {
    super();
  }

  async run(_passedParams: string[], options: CommandOptions): Promise<void> {
    const users = await this.repo.getAllUsers();
    const departments = await this.repo.getAllDepartments();
    const disasters = await this.repo.getAllDisasters();
    const layouts = await this.repo.getAllLayouts();

    const validationErrors: string[] = [];
    if (users.length === 0)
      validationErrors.push('No users found. Please run users:seed first.');
    if (departments.length === 0)
      validationErrors.push(
        'No departments found. Please run initials:seed first.',
      );
    if (disasters.length === 0)
      validationErrors.push(
        'No disasters found. Please run initials:seed first.',
      );
    if (layouts.length === 0)
      validationErrors.push(
        'No layouts found. Please run initials:seed first.',
      );

    if (validationErrors.length > 0 && !options.skipValidation) {
      validationErrors.forEach((error) => console.log(error));
      return;
    }

    let reportsToCreate = reportsMetadataSeed;

    if (options.amount > 0 && options.amount < reportsMetadataSeed.length) {
      reportsToCreate = reportsMetadataSeed.slice(0, options.amount);
    }

    let _createdCount = 0;
    let _approvedCount = 0;
    let _pendingCount = 0;
    let _rejectedCount = 0;
    let _draftCount = 0;
    let _softDeletedCount = 0;
    let _skippedCount = 0;
    let _errorCount = 0;

    for (const reportData of reportsToCreate) {
      try {
        const createdByUser = users.find(
          (u) => u.id === reportData.createdByUserId,
        );
        if (!createdByUser) {
          _skippedCount++;
          continue;
        }

        const layout = layouts.find((l) => l.id === reportData.layoutId);
        if (!layout) {
          _skippedCount++;
          continue;
        }

        const validDisasterIds = reportData.disasterIds.filter((id) =>
          disasters.some((d) => d.id === id),
        );
        if (validDisasterIds.length === 0) {
          _skippedCount++;
          continue;
        }

        const document = await this.repo.createDocument({
          name: reportData.name,
          status: reportData.status,
          actionType: reportData.actionType,
          documentType: DocumentType.Report,
          backgroundColor: reportData.backgroundColor,
          data: reportData.data,
          responsibilityAreaId: reportData.responsibilityAreaId,
          layoutId: reportData.layoutId,
          createdById: reportData.createdByUserId,
          reviewedById: reportData.reviewedByUserId,
          latestRequestedById: reportData.latestRequestedByUserId,
          latestEditedById: reportData.latestEditedByUserId,
          deletedById: reportData.deletedByUserId,
          createdAt: reportData.createdAt,
          reviewedAt: reportData.reviewedAt,
          latestRequestedAt: reportData.latestRequestedAt,
          latestEditedAt: reportData.latestEditedAt,
          deletedAt: reportData.deletedAt,
        });

        for (const disasterId of validDisasterIds) {
          await this.repo.createDocumentDisaster({
            documentId: document.id,
            disasterId: disasterId,
            createdAt: reportData.createdAt,
            updatedAt: reportData.createdAt,
          });
        }

        if (reportData.thumbnailPath) {
          try {
            const thumbnail = await this.repo.createMainDocumentThumbnail({
              resolutionLowPath: reportData.thumbnailPath,
              resolutionHighPath: reportData.thumbnailPath.replace(
                '-low.',
                '-high.',
              ),
              createdAt: reportData.createdAt,
              updatedAt: reportData.createdAt,
            });

            await this.repo.updateDocument(document.id, {
              thumbnailId: thumbnail.id,
            });
          } catch (thumbnailError) {}
        }

        if (reportData.shouldSoftDelete) {
          await this.repo.softDeleteDocument(document.id);
          _softDeletedCount++;
        }

        _createdCount++;

        switch (reportData.status) {
          case 'approved':
            _approvedCount++;
            break;
          case 'waitForApprove':
            _pendingCount++;
            break;
          case 'rejected':
            _rejectedCount++;
            break;
          case 'draft':
            _draftCount++;
            break;
        }
      } catch (error) {
        _errorCount++;
      }
    }

    console.log('Reports seed completed successfully.');
  }

  @Option({
    flags: '-a, --amount [number]',
    defaultValue: '0',
    description: 'Amount of reports to create (0 = all predefined reports)',
  })
  parseAmount(val: string): number {
    return Number(val) || 0;
  }

  @Option({
    flags: '--skip-validation',
    defaultValue: false,
    description: 'Skip validation checks for required entities',
  })
  parseSkipValidation(): boolean {
    return true;
  }

  @Option({
    flags: '-v, --verbose',
    defaultValue: false,
    description: 'Enable verbose output',
  })
  parseVerbose(): boolean {
    return true;
  }
}

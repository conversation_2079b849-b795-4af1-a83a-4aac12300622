import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { CreateTemplateDetails } from '../../templates.v1.type';

export class CreateTemplateV1HttpDto {
  @ApiProperty({
    type: String,
    example: 'Template Name',
  })
  @IsString()
  name: string;

  @ApiProperty({
    type: Number,
    example: 1,
  })
  @IsNumber()
  responsibilityAreaId: number;

  @ApiProperty({
    type: Number,
    example: 1,
  })
  @IsNumber()
  layoutId: number;
}

export class CreateTemplateData implements CreateTemplateDetails {
  id: number;
  name: string;
  status: string;
}

export class CreateTemplatesV1HttpResponse implements IStandardSingleApiResponse {
  success: boolean;
  key: string;
  data: CreateTemplateData;
}

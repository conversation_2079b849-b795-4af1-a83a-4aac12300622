import { Injectable } from '@nestjs/common';

import { Users } from '@core/db/entities/users';
import { MinioService } from '@core/global/minio/minio.service';
import { PermissionValidatorError } from '@core/global/permission-validator/permission-validator.constants';
import { PermissionValidatorService } from '@core/global/permission-validator/permission-validator.service';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';

import { CreateTemplateV1HttpDto } from './handler/dto/create-template.v1.http.dto';
import { GetTemplatesV1HttpParam } from './handler/dto/get-templates.v1.http.dto';
import { TemplateError } from './templates.v1.constant';
import { TemplatesV1Repo } from './templates.v1.repo';
import {
  CreateTemplateDetails,
  GetPaginateTemplates,
  GetTemplateDetail,
  TemplateDetails,
  TemplateErrorType,
} from './templates.v1.type';

@Injectable()
export class TemplatesV1Service {
  constructor(
    private templateRepo: TemplatesV1Repo,
    private minioService: MinioService,
    private permissionValidator: PermissionValidatorService,
  ) {}

  async getPaginateTemplates(
    user: Users,
    options: GetTemplatesV1HttpParam,
  ): Promise<Res<GetPaginateTemplates, ''>> {
    const query = {
      search: options.search,
      startDate: options.startDate,
      endDate: options.endDate,
      departmentIds: options.departmentIds,
      layoutDirections: options.layoutDirections,
      disasterIds: options.disasterIds,
      orderBy: options.orderBy || TypeOrmOrderBy.DESC,
      sortBy: options.sortBy || 'reviewedAt',
    };

    const { datas, pagination } = await this.templateRepo.getPaginateTemplates(
      {
        page: options.page,
        perPage: options.perPage,
      },
      query,
    );

    // Transform the data to include thumbnail URL and permissions
    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        const permission = {
          canDelete: this.permissionValidator.canDeleteTemplate({
            user,
            createdBy,
            documentStatus: other.status,
            department: other.department,
          }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    const totalApproved = await this.templateRepo.getApprovedTemplateCounts();
    const totalPending = await this.templateRepo.getPendingTemplateCounts();
    const totalTemplates = await this.templateRepo.getTotalTemplateCounts();

    return Ok({
      datas: transformedData,
      pagination,
      totalApproved,
      totalPending,
      totalTemplates,
    });
  }

  async getTemplateDetailById(
    id: number,
    user: Users,
  ): Promise<Res<GetTemplateDetail, 'notFound'>> {
    const template = await this.templateRepo.getTemplateDetailById(id);

    if (!template) {
      return Err('notFound');
    }

    const permission = {
      canCreate: this.permissionValidator.canCreateTemplate({
        user,
        createdBy: template.createdBy,
        area: {
          id: template.responsibilityAreaId,
        },
      }),
      canEdit: this.permissionValidator.canEditTemplate({
        user,
        createdBy: template.createdBy,
        documentStatus: template.status,
        department: template.createdBy.department,
      }),
    };

    const transformedData: TemplateDetails = {
      id: template.id,
      name: template.name,
      status: template.status,
      actionType: template.actionType,
      documentType: template.documentType,
      data: template.data,
      backgroundColor: template.backgroundColor,

      thumbnailUrl: await this.minioService
        .getPresignedUrl({
          object: template.thumbnail?.resolutionHighPath || '',
        })
        .catch(() => ''),

      layout: {
        id: template.layout.id,
        direction: template.layout.direction,
      },

      department: {
        id: template.createdBy.department?.id,
        nameTh: template.createdBy.department?.nameTh,
      },

      disasters: template.disasters?.map((disaster) => ({
        id: disaster.id,
        name: disaster.name,
      })),

      createdBy: {
        id: template.createdBy.id,
        firstname: template.createdBy.firstname,
        lastname: template.createdBy.lastname,
        isMadeByMe: user.id === template.createdBy.id,
      },

      reviewedBy: {
        id: template.reviewedBy?.id,
        firstname: template.reviewedBy?.firstname,
        lastname: template.reviewedBy?.lastname,
        isMadeByMe: user.id === template.reviewedBy?.id,
      },

      latestRequestedBy: {
        id: template.latestRequestedBy?.id,
        firstname: template.latestRequestedBy?.firstname,
        lastname: template.latestRequestedBy?.lastname,
        isMadeByMe: user.id === template.latestRequestedBy?.id,
      },

      latestEditedBy: {
        id: template.latestEditedBy?.id,
        firstname: template.latestEditedBy?.firstname,
        lastname: template.latestEditedBy?.lastname,
        isMadeByMe: user.id === template.latestEditedBy?.id,
      },

      createdAt: template.createdAt,
      reviewedAt: template.reviewedAt,
      latestRequestedAt: template.latestRequestedAt,
      latestEditedAt: template.latestEditedAt,

      permission,
    };

    return Ok({
      datas: transformedData,
    });
  }

  async createDraftTemplate(
    body: CreateTemplateV1HttpDto,
    user: Users,
  ): Promise<Res<{ datas: CreateTemplateDetails }, TemplateErrorType>> {
    const canCreate = this.permissionValidator.canCreateTemplate({
      user,
      area: {
        id: body.responsibilityAreaId,
      },
    });

    if (!canCreate) {
      return Err(PermissionValidatorError.PermissionDenied);
    }

    const existedTemplate = await this.templateRepo.getOneTemplate({
      where: {
        name: body.name?.trim(),
      },
    });

    if (existedTemplate) {
      return Err(TemplateError.DuplicateName);
    }

    const createdTemplate = await this.templateRepo.createDraftTemplate({
      ...body,
      createdById: user.id,
    });

    const transformedTemplate: CreateTemplateDetails = {
      id: createdTemplate.id,
      name: createdTemplate.name,
      status: createdTemplate.status,
    };

    return Ok({
      datas: transformedTemplate,
    });
  }
}

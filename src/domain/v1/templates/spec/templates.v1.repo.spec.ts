import { DataSource, SelectQueryBuilder } from 'typeorm';
import { mock } from 'jest-mock-extended';

import { Documents } from '@core/db/entities/documents';
import tzDayjs from '@core/shared/common/common.dayjs';
import { createRepoTestingModule } from '@core/test/test-util/test-util.common';

import { ApproveStatus, DocumentActionType, DocumentType } from '../../documents/documents.v1.constant';
import { TemplatesV1Repo } from '../templates.v1.repo';

describe('TemplatesV1Repo', () => {
  let repo: TemplatesV1Repo;
  let dataSource: DataSource;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<Documents>>;

  beforeAll(async () => {
    // Check if we have global dataSource for real DB testing
    if (globalThis.dataSource) {
      const module = await createRepoTestingModule(TemplatesV1Repo);
      repo = module.get(TemplatesV1Repo);
      dataSource = globalThis.dataSource;
    } else {
      // Mock setup for unit testing without DB
      dataSource = mock<DataSource>();
      repo = new TemplatesV1Repo(dataSource, {} as any);
    }
  });

  beforeEach(() => {
    // Setup mock query builder for each test
    mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getCount: jest.fn(),
      countBy: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
    } as any;

    if (!globalThis.dataSource) {
      // Mock the repository's from method
      const mockRepository = {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
        countBy: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
      };
      jest.spyOn(repo, 'from').mockReturnValue(mockRepository as any);
    }
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getPaginateTemplates', () => {
    const mockCurrentTime = tzDayjs('2024-01-15T10:00:00Z');
    
    const mockTemplateData = {
      id: 1,
      name: 'Test Template',
      status: ApproveStatus.Approved,
      actionType: DocumentActionType.Create,
      backgroundColor: '#FFFFFF',
      reviewedAt: mockCurrentTime.subtract(1, 'day').toDate(),
      createdAt: mockCurrentTime.subtract(2, 'days').toDate(),
      latestEditedAt: mockCurrentTime.subtract(1, 'hour').toDate(),
      thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
      createdBy: { 
        id: 1,
        department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' }
      },
      layout: { id: 1 },
      disasters: [{ id: 1 }],
    } as any;

    const paginationOptions = { page: 1, perPage: 10 };
    const basicQuery = {
      orderBy: 'DESC' as const,
      sortBy: 'reviewedAt',
    };

    it('should construct correct base query for templates', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockTemplateData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      await repo.getPaginateTemplates(paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(repo.from).toHaveBeenCalledWith(Documents);
        const mockRepository = (repo.from as jest.Mock).mock.results[0].value;
        expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('doc');
        
        expect(mockQueryBuilder.select).toHaveBeenCalledWith([
          'doc.id',
          'doc.name',
          'doc.status',
          'doc.actionType',
          'doc.backgroundColor',
          'doc.reviewedAt',
          'doc.createdAt',
          'doc.latestEditedAt',
          'layout.id',
          'disaster.id',
          'thumbnail.resolutionLowPath',
          'createdBy.id',
          'department.id',
          'department.nameTh',
        ]);

        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('doc.layout', 'layout');
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('doc.disasters', 'disaster');
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('doc.thumbnail', 'thumbnail');
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('doc.createdBy', 'createdBy');
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('createdBy.department', 'department');

        expect(mockQueryBuilder.where).toHaveBeenCalledWith('doc.documentType = :documentType', {
          documentType: DocumentType.Template,
        });
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.status = :status', {
          status: ApproveStatus.Approved,
        });
      }
    });

    it('should apply search filter correctly', async () => {
      const queryWithSearch = { ...basicQuery, search: 'Test Template' };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithSearch);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.name ILIKE :search', {
          search: '%Test Template%',
        });
      }
    });

    it('should trim search query', async () => {
      const queryWithSearch = { ...basicQuery, search: '  Test Template  ' };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithSearch);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.name ILIKE :search', {
          search: '%Test Template%',
        });
      }
    });

    it('should apply date range filters correctly', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const queryWithDates = { ...basicQuery, startDate, endDate };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithDates);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.reviewedAt >= :startDate', {
          startDate: tzDayjs(startDate).startOf('day').toDate(),
        });
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('doc.reviewedAt <= :endDate', {
          endDate: tzDayjs(endDate).endOf('day').toDate(),
        });
      }
    });

    it('should apply department filter correctly', async () => {
      const queryWithDepartments = { ...basicQuery, departmentIds: [1, 2, 3] };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithDepartments);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('department.id IN (:...departmentIds)', {
          departmentIds: [1, 2, 3],
        });
      }
    });

    it('should apply layout directions filter correctly', async () => {
      const queryWithLayouts = { ...basicQuery, layoutDirections: ['horizontal', 'vertical'] };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithLayouts);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('layout.direction IN (:...layoutDirections)', {
          layoutDirections: ['horizontal', 'vertical'],
        });
      }
    });

    it('should apply disaster filter correctly', async () => {
      const queryWithDisasters = { ...basicQuery, disasterIds: [1, 2] };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithDisasters);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('disaster.id IN (:...disasterIds)', {
          disasterIds: [1, 2],
        });
      }
    });

    it('should not apply filters when arrays are empty', async () => {
      const queryWithEmptyArrays = { 
        ...basicQuery, 
        departmentIds: [], 
        layoutDirections: [], 
        disasterIds: [] 
      };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(paginationOptions, queryWithEmptyArrays);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('department.id IN'),
          expect.anything()
        );
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('layout.direction IN'),
          expect.anything()
        );
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('disaster.id IN'),
          expect.anything()
        );
      }
    });

    it('should apply correct sorting for different sortBy options', async () => {
      const testCases = [
        { sortBy: 'name', orderBy: 'ASC' as const },
        { sortBy: 'latestEditedAt', orderBy: 'DESC' as const },
        { sortBy: 'createdAt', orderBy: 'ASC' as const },
        { sortBy: 'reviewedAt', orderBy: 'DESC' as const },
        { sortBy: 'unknown', orderBy: 'ASC' as const }, // Should default to reviewedAt
      ];

      for (const testCase of testCases) {
        if (!globalThis.dataSource) {
          mockQueryBuilder.getMany.mockResolvedValue([]);
          mockQueryBuilder.getCount.mockResolvedValue(0);
          jest.clearAllMocks();
        }

        await repo.getPaginateTemplates(paginationOptions, testCase);

        if (!globalThis.dataSource) {
          if (testCase.sortBy === 'name') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('doc.name', testCase.orderBy);
          } else if (testCase.sortBy === 'latestEditedAt') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('doc.latestEditedAt', testCase.orderBy);
          } else if (testCase.sortBy === 'createdAt') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('doc.createdAt', testCase.orderBy);
          } else {
            // Default to reviewedAt for unknown sortBy
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('doc.reviewedAt', testCase.orderBy);
          }
        }
      }
    });

    it('should apply pagination correctly', async () => {
      const customPagination = { page: 3, perPage: 20 };
      
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateTemplates(customPagination, basicQuery);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.take).toHaveBeenCalledWith(20); // perPage
        expect(mockQueryBuilder.skip).toHaveBeenCalledWith(40); // (page - 1) * perPage = (3 - 1) * 20
      }
    });

    it('should return correctly formatted data structure', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockTemplateData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      const result = await repo.getPaginateTemplates(paginationOptions, basicQuery);

      expect(result).toHaveProperty('datas');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.datas)).toBe(true);

      if (!globalThis.dataSource) {
        expect(result.datas).toHaveLength(1);
        expect(result.datas[0]).toEqual({
          id: 1,
          name: 'Test Template',
          status: ApproveStatus.Approved,
          actionType: DocumentActionType.Create,
          backgroundColor: '#FFFFFF',
          thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
          createdBy: { id: 1 },
          department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' },
        });
      }
    });

    it('should handle documents with missing relationships', async () => {
      const mockDataWithMissingRels = {
        ...mockTemplateData,
        thumbnail: null,
        createdBy: { 
          id: 1,
          department: null
        },
      } as any;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockDataWithMissingRels]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      const result = await repo.getPaginateTemplates(paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(result.datas[0]).toEqual({
          id: 1,
          name: 'Test Template',
          status: ApproveStatus.Approved,
          actionType: DocumentActionType.Create,
          backgroundColor: '#FFFFFF',
          thumbnail: null,
          createdBy: { id: 1 },
          department: { id: undefined, nameTh: undefined },
        });
      }
    });

    it('should handle empty results', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      const result = await repo.getPaginateTemplates(paginationOptions, basicQuery);

      expect(result.datas).toEqual([]);
      expect(result.pagination).toBeDefined();
    });
  });
});

import { mock } from 'jest-mock-extended';
import { DataSource, SelectQueryBuilder } from 'typeorm';

import { Documents } from '@core/db/entities/documents';
import tzDayjs from '@core/shared/common/common.dayjs';
import { createRepoTestingModule } from '@core/test/test-util/test-util.common';

import {
  ApproveStatus,
  DocumentActionType,
  DocumentType,
} from '../../documents/documents.v1.constant';
import { ReportsV1Repo } from '../reports.v1.repo';

describe('ReportsV1Repo', () => {
  let repo: ReportsV1Repo;
  let dataSource: DataSource;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<Documents>>;

  beforeAll(async () => {
    if (globalThis.dataSource) {
      const module = await createRepoTestingModule(ReportsV1Repo);
      repo = module.get(ReportsV1Repo);
      dataSource = globalThis.dataSource;
    } else {
      dataSource = mock<DataSource>();
      repo = new ReportsV1Repo(dataSource, {} as any);
    }
  });

  beforeEach(() => {
    mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
      getCount: jest.fn(),
    } as any;

    if (!globalThis.dataSource) {
      const mockRepository = {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
      };
      jest.spyOn(repo, 'from').mockReturnValue(mockRepository as any);
    }
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getPaginateReports', () => {
    const mockCurrentTime = tzDayjs('2024-01-15T10:00:00Z');

    const mockReportData = {
      id: 1,
      name: 'Test Report',
      status: ApproveStatus.Approved,
      actionType: DocumentActionType.Create,
      backgroundColor: '#F0F0F0',
      reviewedAt: mockCurrentTime.subtract(1, 'day').toDate(),
      createdAt: mockCurrentTime.subtract(2, 'days').toDate(),
      latestEditedAt: mockCurrentTime.subtract(1, 'hour').toDate(),
      responsibilityAreaId: 1,
      thumbnail: { resolutionLowPath: 'report-thumb.jpg' },
      createdBy: {
        id: 1,
        department: { id: 1, nameTh: 'กรมอุตุนิยมวิทยา' },
      },
      layout: { id: 1 },
      disasters: [{ id: 1 }],
    } as any;

    const paginationOptions = { page: 1, perPage: 10 };
    const basicQuery = {
      orderBy: 'DESC' as const,
      sortBy: 'reviewedAt',
    };

    it('should construct correct base query for reports', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockReportData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      await repo.getPaginateReports(paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(repo.from).toHaveBeenCalledWith(Documents);
        const mockRepository = (repo.from as jest.Mock).mock.results[0].value;
        expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('doc');

        expect(mockQueryBuilder.select).toHaveBeenCalledWith([
          'doc.id',
          'doc.name',
          'doc.status',
          'doc.actionType',
          'doc.backgroundColor',
          'doc.reviewedAt',
          'doc.createdAt',
          'doc.latestEditedAt',
          'doc.responsibilityAreaId',
          'layout.id',
          'disaster.id',
          'thumbnail.resolutionLowPath',
          'createdBy.id',
          'department.id',
          'department.nameTh',
        ]);

        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.layout',
          'layout',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.disasters',
          'disaster',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.thumbnail',
          'thumbnail',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.createdBy',
          'createdBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'createdBy.department',
          'department',
        );

        expect(mockQueryBuilder.where).toHaveBeenCalledWith(
          'doc.documentType = :documentType',
          {
            documentType: DocumentType.Report,
          },
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.status = :status',
          {
            status: ApproveStatus.Approved,
          },
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.deletedAt IS NULL',
        );
      }
    });

    it('should include soft delete filter', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, basicQuery);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.deletedAt IS NULL',
        );
      }
    });

    it('should apply search filter correctly', async () => {
      const queryWithSearch = { ...basicQuery, search: 'Test Report' };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithSearch);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.name ILIKE :search',
          {
            search: '%Test Report%',
          },
        );
      }
    });

    it('should trim search query', async () => {
      const queryWithSearch = { ...basicQuery, search: '  Test Report  ' };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithSearch);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.name ILIKE :search',
          {
            search: '%Test Report%',
          },
        );
      }
    });

    it('should apply date range filters correctly', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const queryWithDates = { ...basicQuery, startDate, endDate };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithDates);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.reviewedAt >= :startDate',
          {
            startDate: tzDayjs(startDate).startOf('day').toDate(),
          },
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.reviewedAt <= :endDate',
          {
            endDate: tzDayjs(endDate).endOf('day').toDate(),
          },
        );
      }
    });

    it('should apply department filter correctly', async () => {
      const queryWithDepartments = { ...basicQuery, departmentIds: [1, 2, 3] };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithDepartments);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'department.id IN (:...departmentIds)',
          {
            departmentIds: [1, 2, 3],
          },
        );
      }
    });

    it('should apply layout directions filter correctly', async () => {
      const queryWithLayouts = {
        ...basicQuery,
        layoutDirections: ['horizontal', 'vertical'],
      };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithLayouts);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'layout.direction IN (:...layoutDirections)',
          {
            layoutDirections: ['horizontal', 'vertical'],
          },
        );
      }
    });

    it('should apply disaster filter correctly', async () => {
      const queryWithDisasters = { ...basicQuery, disasterIds: [1, 2] };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithDisasters);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'disaster.id IN (:...disasterIds)',
          {
            disasterIds: [1, 2],
          },
        );
      }
    });

    it('should not apply filters when arrays are empty', async () => {
      const queryWithEmptyArrays = {
        ...basicQuery,
        departmentIds: [],
        layoutDirections: [],
        disasterIds: [],
      };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(paginationOptions, queryWithEmptyArrays);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('department.id IN'),
          expect.anything(),
        );
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('layout.direction IN'),
          expect.anything(),
        );
        expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
          expect.stringContaining('disaster.id IN'),
          expect.anything(),
        );
      }
    });

    it('should apply correct sorting for different sortBy options', async () => {
      const testCases = [
        { sortBy: 'name', orderBy: 'ASC' as const },
        { sortBy: 'latestEditedAt', orderBy: 'DESC' as const },
        { sortBy: 'createdAt', orderBy: 'ASC' as const },
        { sortBy: 'reviewedAt', orderBy: 'DESC' as const },
        { sortBy: 'unknown', orderBy: 'ASC' as const },
      ];

      for (const testCase of testCases) {
        if (!globalThis.dataSource) {
          mockQueryBuilder.getMany.mockResolvedValue([]);
          mockQueryBuilder.getCount.mockResolvedValue(0);
          jest.clearAllMocks();
        }

        await repo.getPaginateReports(paginationOptions, testCase);

        if (!globalThis.dataSource) {
          if (testCase.sortBy === 'name') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
              'doc.name',
              testCase.orderBy,
            );
          } else if (testCase.sortBy === 'latestEditedAt') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
              'doc.latestEditedAt',
              testCase.orderBy,
            );
          } else if (testCase.sortBy === 'createdAt') {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
              'doc.createdAt',
              testCase.orderBy,
            );
          } else {
            expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
              'doc.reviewedAt',
              testCase.orderBy,
            );
          }
        }
      }
    });

    it('should apply pagination correctly', async () => {
      const customPagination = { page: 3, perPage: 20 };

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      await repo.getPaginateReports(customPagination, basicQuery);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.take).toHaveBeenCalledWith(20);
        expect(mockQueryBuilder.skip).toHaveBeenCalledWith(40);
      }
    });

    it('should return correctly formatted data structure', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockReportData]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      const result = await repo.getPaginateReports(
        paginationOptions,
        basicQuery,
      );

      expect(result).toHaveProperty('datas');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.datas)).toBe(true);

      if (!globalThis.dataSource) {
        expect(result.datas).toHaveLength(1);
        expect(result.datas[0]).toEqual({
          id: 1,
          name: 'Test Report',
          status: ApproveStatus.Approved,
          actionType: DocumentActionType.Create,
          backgroundColor: '#F0F0F0',
          thumbnail: { resolutionLowPath: 'report-thumb.jpg' },
          createdBy: { id: 1 },
          department: { id: 1, nameTh: 'กรมอุตุนิยมวิทยา' },
        });
      }
    });

    it('should handle documents with missing relationships', async () => {
      const mockDataWithMissingRels = {
        ...mockReportData,
        thumbnail: null,
        createdBy: {
          id: 1,
          department: null,
        },
      } as any;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockDataWithMissingRels]);
        mockQueryBuilder.getCount.mockResolvedValue(1);
      }

      const result = await repo.getPaginateReports(
        paginationOptions,
        basicQuery,
      );

      if (!globalThis.dataSource) {
        expect(result.datas[0]).toEqual({
          id: 1,
          name: 'Test Report',
          status: ApproveStatus.Approved,
          actionType: DocumentActionType.Create,
          backgroundColor: '#F0F0F0',
          thumbnail: null,
          createdBy: { id: 1 },
          department: { id: undefined, nameTh: undefined },
        });
      }
    });

    it('should handle empty results', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
        mockQueryBuilder.getCount.mockResolvedValue(0);
      }

      const result = await repo.getPaginateReports(
        paginationOptions,
        basicQuery,
      );

      expect(result.datas).toEqual([]);
      expect(result.pagination).toBeDefined();
    });
  });

  describe('getReportDetailsById', () => {
    const mockDetailData = {
      id: 1,
      name: 'Test Report Detail',
      status: ApproveStatus.Approved,
      actionType: DocumentActionType.Create,
      documentType: DocumentType.Report,
      backgroundColor: '#F0F0F0',
      data: { title: 'Test Report', content: 'Report Content' },
      reviewedAt: new Date(),
      latestRequestedAt: new Date(),
      latestEditedAt: new Date(),
      createdAt: new Date(),
      layout: { id: 1, direction: 'horizontal' },
      disasters: [{ id: 1, name: 'Flood' }],
      thumbnail: { resolutionHighPath: 'high-res-report-thumb.jpg' },
      reviewedBy: { id: 2, firstname: 'John', lastname: 'Doe' },
      latestRequestedBy: { id: 3, firstname: 'Jane', lastname: 'Smith' },
      latestEditedBy: { id: 4, firstname: 'Bob', lastname: 'Wilson' },
      createdBy: {
        id: 1,
        firstname: 'Alice',
        lastname: 'Johnson',
        department: { id: 1, nameTh: 'กรมอุตุนิยมวิทยา' },
      },
    } as any;

    it('should construct correct query for report details', async () => {
      const reportId = 1;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getOne.mockResolvedValue(mockDetailData);
      }

      await repo.getReportDetailsById(reportId);

      if (!globalThis.dataSource) {
        expect(repo.from).toHaveBeenCalledWith(Documents);
        const mockRepository = (repo.from as jest.Mock).mock.results[0].value;
        expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('doc');

        expect(mockQueryBuilder.select).toHaveBeenCalledWith([
          'doc.id',
          'doc.name',
          'doc.status',
          'doc.actionType',
          'doc.documentType',
          'doc.backgroundColor',
          'doc.data',
          'layout.id',
          'layout.direction',
          'disaster.id',
          'disaster.name',
          'thumbnail.resolutionHighPath',
          'department.id',
          'department.nameTh',
          'doc.reviewedAt',
          'reviewedBy.id',
          'reviewedBy.firstname',
          'reviewedBy.lastname',
          'doc.latestRequestedAt',
          'latestRequestedBy.id',
          'latestRequestedBy.firstname',
          'latestRequestedBy.lastname',
          'doc.latestEditedAt',
          'latestEditedBy.id',
          'latestEditedBy.firstname',
          'latestEditedBy.lastname',
          'doc.createdAt',
          'createdBy.id',
          'createdBy.firstname',
          'createdBy.lastname',
        ]);

        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.layout',
          'layout',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.disasters',
          'disaster',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.thumbnail',
          'thumbnail',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.reviewedBy',
          'reviewedBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.latestRequestedBy',
          'latestRequestedBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.latestEditedBy',
          'latestEditedBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.createdBy',
          'createdBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'createdBy.department',
          'department',
        );

        expect(mockQueryBuilder.where).toHaveBeenCalledWith('doc.id = :id', {
          id: reportId,
        });
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.documentType = :documentType',
          {
            documentType: DocumentType.Report,
          },
        );
      }
    });

    it('should return report detail data', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getOne.mockResolvedValue(mockDetailData);
      }

      const result = await repo.getReportDetailsById(1);

      if (!globalThis.dataSource) {
        expect(result).toEqual(mockDetailData);
      } else {
        expect(result).toBeDefined();
      }
    });

    it('should return null for non-existent report', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getOne.mockResolvedValue(null);
      }

      const result = await repo.getReportDetailsById(999);

      if (!globalThis.dataSource) {
        expect(result).toBeNull();
      }
    });

    it('should not include responsibilityAreaId in select', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getOne.mockResolvedValue(mockDetailData);
      }

      await repo.getReportDetailsById(1);

      if (!globalThis.dataSource) {
        const selectCall = mockQueryBuilder.select.mock.calls[0][0];
        expect(selectCall).not.toContain('doc.responsibilityAreaId');
      }
    });
  });

  describe('count methods', () => {
    describe('getApprovedReportCounts', () => {
      it('should count approved reports correctly with soft delete check', async () => {
        if (!globalThis.dataSource) {
          mockQueryBuilder.getCount.mockResolvedValue(5);
        }

        const result = await repo.getApprovedReportCounts();

        if (!globalThis.dataSource) {
          expect(mockQueryBuilder.where).toHaveBeenCalledWith(
            'doc.documentType = :documentType',
            {
              documentType: DocumentType.Report,
            },
          );
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
            'doc.status = :status',
            {
              status: ApproveStatus.Approved,
            },
          );
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
            'doc.deletedAt IS NULL',
          );
          expect(result).toBe(5);
        } else {
          expect(typeof result).toBe('number');
        }
      });
    });

    describe('getTotalReportCounts', () => {
      it('should count total non-deleted reports', async () => {
        if (!globalThis.dataSource) {
          mockQueryBuilder.getCount.mockResolvedValue(10);
        }

        const result = await repo.getTotalReportCounts();

        if (!globalThis.dataSource) {
          expect(mockQueryBuilder.where).toHaveBeenCalledWith(
            'doc.documentType = :documentType',
            {
              documentType: DocumentType.Report,
            },
          );
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
            'doc.deletedAt IS NULL',
          );
          expect(result).toBe(10);
        } else {
          expect(typeof result).toBe('number');
        }
      });
    });

    describe('getPendingReportCounts', () => {
      it('should count pending reports correctly', async () => {
        if (!globalThis.dataSource) {
          mockQueryBuilder.getCount.mockResolvedValue(3);
        }

        const result = await repo.getPendingReportCounts();

        if (!globalThis.dataSource) {
          expect(mockQueryBuilder.where).toHaveBeenCalledWith(
            'doc.documentType = :documentType',
            {
              documentType: DocumentType.Report,
            },
          );
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
            'doc.status != :status',
            {
              status: ApproveStatus.Approved,
            },
          );
          expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
            'doc.deletedAt IS NULL',
          );
          expect(result).toBe(3);
        } else {
          expect(typeof result).toBe('number');
        }
      });
    });
  });
});

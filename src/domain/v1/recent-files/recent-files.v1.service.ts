import { Injectable } from '@nestjs/common';

import { Users } from '@core/db/entities/users';
import { MinioService } from '@core/global/minio/minio.service';
import { PermissionValidatorService } from '@core/global/permission-validator/permission-validator.service';
import { Ok, Res } from '@core/shared/common/common.neverthrow';

import { DocumentType } from '../documents/documents.v1.constant';
import { RecentFilesV1Repo } from './recent-files.v1.repo';
import { GetRecentFiles } from './recent-files.v1.type';

@Injectable()
export class RecentFilesV1Service {
  constructor(
    private recentFilesRepo: RecentFilesV1Repo,
    private minioService: MinioService,
    private permissionValidator: PermissionValidatorService,
  ) {}

  async getRecentFiles(
    user: Users,
    limit: number = 10,
  ): Promise<Res<GetRecentFiles, ''>> {
    const { datas } = await this.recentFilesRepo.getRecentFiles(limit);

    const transformedData = await Promise.all(
      datas.map(async (data) => {
        const { thumbnail, createdBy, ...other } = data;

        const thumbnailUrl = await this.minioService
          .getPresignedUrl({
            object: thumbnail?.resolutionLowPath || '',
          })
          .catch(() => '');

        // Determine permission based on document type
        const permission = {
          canDelete:
            other.documentType === DocumentType.Template
              ? this.permissionValidator.canDeleteTemplate({
                  user,
                  createdBy,
                  documentStatus: other.status,
                  department: other.department,
                })
              : this.permissionValidator.canDeleteReport({
                  user,
                  createdBy,
                  documentStatus: other.status,
                  department: other.department,
                }),
        };

        return {
          ...other,
          thumbnailUrl,
          permission,
        };
      }),
    );

    return Ok({
      datas: transformedData,
    });
  }
}

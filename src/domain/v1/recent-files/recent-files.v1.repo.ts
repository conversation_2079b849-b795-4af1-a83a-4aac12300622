import { Injectable } from '@nestjs/common';

import { Documents } from '@core/db/entities/documents';
import { BaseRepo } from '@core/shared/common/common.repo';

import {
  ApproveStatus,
  DocumentType,
} from '../documents/documents.v1.constant';

@Injectable()
export class RecentFilesV1Repo extends BaseRepo {
  async getRecentFiles(limit: number = 10) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.documentType',
        'doc.backgroundColor',
        'doc.latestEditedAt',
        'thumbnail.resolutionLowPath',
        'createdBy.id',
        'department.id',
        'department.nameTh',
      ])
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.documentType IN (:...documentTypes)', {
        documentTypes: [DocumentType.Template, DocumentType.Report],
      })
      .andWhere('doc.status = :status', {
        status: ApproveStatus.Approved,
      })
      .andWhere('doc.latestEditedAt IS NOT NULL')
      .andWhere('doc.deletedAt IS NULL')
      .orderBy('doc.latestEditedAt', 'DESC')
      .limit(limit);

    const datas = await qb.getMany();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        name: data.name,
        status: data.status,
        actionType: data.actionType,
        documentType: data.documentType,
        backgroundColor: data.backgroundColor,
        latestEditedAt: data.latestEditedAt,
        thumbnail: data.thumbnail,
        createdBy: {
          id: data.createdBy.id,
        },
        department: {
          id: data.createdBy?.department?.id,
          nameTh: data.createdBy?.department?.nameTh,
        },
      })),
    };
  }
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, Max, Min } from 'class-validator';

import { IStandardArrayApiResponse } from '@core/shared/http/http.standard';

import { RecentFileDetails } from '../../recent-files.v1.type';

export class GetRecentFilesV1HttpParam {
  @ApiPropertyOptional({
    type: Number,
    example: 10,
    description: 'Number of recent files to return (max 50)',
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number;
}

class GetRecentFilesV1HttpData implements RecentFileDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  documentType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  latestEditedAt: Date | null;
  department: { id: number; nameTh: string };
  permission: { canDelete: boolean };
}

export class GetRecentFilesV1HttpResponse implements IStandardArrayApiResponse {
  success: boolean;
  key: string;
  data: GetRecentFilesV1HttpData[];
}

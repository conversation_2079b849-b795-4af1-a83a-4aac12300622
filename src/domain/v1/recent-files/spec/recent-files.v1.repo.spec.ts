import { mock } from 'jest-mock-extended';
import { DataSource, QueryBuilder, SelectQueryBuilder } from 'typeorm';

import { Documents } from '@core/db/entities/documents';
import tzDayjs from '@core/shared/common/common.dayjs';
import { createRepoTestingModule } from '@core/test/test-util/test-util.common';

import {
  ApproveStatus,
  DocumentType,
} from '../../documents/documents.v1.constant';
import { RecentFilesV1Repo } from '../recent-files.v1.repo';

describe('RecentFilesV1Repo', () => {
  let repo: RecentFilesV1Repo;
  let dataSource: DataSource;
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<Documents>>;

  beforeAll(async () => {
    // Check if we have global dataSource for real DB testing
    if (globalThis.dataSource) {
      const module = await createRepoTestingModule(RecentFilesV1Repo);
      repo = module.get(RecentFilesV1Repo);
      dataSource = globalThis.dataSource;
    } else {
      // Mock setup for unit testing without DB
      dataSource = mock<DataSource>();
      repo = new RecentFilesV1Repo(dataSource, {} as any);
    }
  });

  beforeEach(() => {
    // Setup mock query builder for each test
    mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    } as any;

    if (!globalThis.dataSource) {
      // Mock the repository's from method
      const mockRepository = {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
      };
      jest.spyOn(repo, 'from').mockReturnValue(mockRepository as any);
    }
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getRecentFiles', () => {
    const mockCurrentTime = tzDayjs('2024-01-15T10:00:00Z');

    const mockTemplateData = {
      id: 1,
      name: 'Test Template',
      status: ApproveStatus.Approved,
      actionType: 'create',
      documentType: DocumentType.Template,
      backgroundColor: '#FFFFFF',
      latestEditedAt: mockCurrentTime.subtract(1, 'day').toDate(),
      thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
      createdBy: {
        id: 1,
        department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' },
      },
    } as any;

    const mockReportData = {
      id: 2,
      name: 'Test Report',
      status: ApproveStatus.Approved,
      actionType: 'create',
      documentType: DocumentType.Report,
      backgroundColor: '#F0F0F0',
      latestEditedAt: mockCurrentTime.subtract(2, 'hours').toDate(),
      thumbnail: { resolutionLowPath: 'report-thumb.jpg' },
      createdBy: {
        id: 2,
        department: { id: 2, nameTh: 'กรมอุตุนิยมวิทยา' },
      },
    } as any;

    it('should return recent files ordered by latestEditedAt DESC', async () => {
      const mockResults = [mockReportData, mockTemplateData]; // Report is more recent

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue(mockResults);
      }

      const result = await repo.getRecentFiles(10);

      // Verify query builder calls
      if (!globalThis.dataSource) {
        expect(repo.from).toHaveBeenCalledWith(Documents);
        const mockRepository = (repo.from as jest.Mock).mock.results[0].value;
        expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith('doc');
        expect(mockQueryBuilder.select).toHaveBeenCalledWith([
          'doc.id',
          'doc.name',
          'doc.status',
          'doc.actionType',
          'doc.documentType',
          'doc.backgroundColor',
          'doc.latestEditedAt',
          'thumbnail.resolutionLowPath',
          'createdBy.id',
          'department.id',
          'department.nameTh',
        ]);
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.thumbnail',
          'thumbnail',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'doc.createdBy',
          'createdBy',
        );
        expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith(
          'createdBy.department',
          'department',
        );
        expect(mockQueryBuilder.where).toHaveBeenCalledWith(
          'doc.documentType IN (:...documentTypes)',
          {
            documentTypes: [DocumentType.Template, DocumentType.Report],
          },
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.status = :status',
          {
            status: ApproveStatus.Approved,
          },
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.latestEditedAt IS NOT NULL',
        );
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.deletedAt IS NULL',
        );
        expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
          'doc.latestEditedAt',
          'DESC',
        );
        expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
      }

      // Verify result structure
      expect(result).toHaveProperty('datas');
      expect(Array.isArray(result.datas)).toBe(true);

      if (!globalThis.dataSource) {
        expect(result.datas).toHaveLength(2);

        // Verify first item (most recent - report)
        expect(result.datas[0]).toEqual({
          id: 2,
          name: 'Test Report',
          status: ApproveStatus.Approved,
          actionType: 'create',
          documentType: DocumentType.Report,
          backgroundColor: '#F0F0F0',
          latestEditedAt: mockCurrentTime.subtract(2, 'hours').toDate(),
          thumbnail: { resolutionLowPath: 'report-thumb.jpg' },
          createdBy: { id: 2 },
          department: { id: 2, nameTh: 'กรมอุตุนิยมวิทยา' },
        });

        // Verify second item (older - template)
        expect(result.datas[1]).toEqual({
          id: 1,
          name: 'Test Template',
          status: ApproveStatus.Approved,
          actionType: 'create',
          documentType: DocumentType.Template,
          backgroundColor: '#FFFFFF',
          latestEditedAt: mockCurrentTime.subtract(1, 'day').toDate(),
          thumbnail: { resolutionLowPath: 'template-thumb.jpg' },
          createdBy: { id: 1 },
          department: { id: 1, nameTh: 'กรมป้องกันและบรรเทาสาธารณภัย' },
        });
      }
    });

    it('should apply custom limit correctly', async () => {
      const customLimit = 5;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles(customLimit);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.limit).toHaveBeenCalledWith(customLimit);
      }
    });

    it('should use default limit of 10 when no limit provided', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles();

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
      }
    });

    it('should handle empty results', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      const result = await repo.getRecentFiles(10);

      expect(result).toHaveProperty('datas');
      expect(result.datas).toEqual([]);
    });

    it('should only include approved documents', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.status = :status',
          {
            status: ApproveStatus.Approved,
          },
        );
      }
    });

    it('should only include templates and reports', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.where).toHaveBeenCalledWith(
          'doc.documentType IN (:...documentTypes)',
          {
            documentTypes: [DocumentType.Template, DocumentType.Report],
          },
        );
      }
    });

    it('should exclude documents without latestEditedAt', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.latestEditedAt IS NOT NULL',
        );
      }
    });

    it('should exclude soft-deleted documents', async () => {
      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([]);
      }

      await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
          'doc.deletedAt IS NULL',
        );
      }
    });

    it('should handle documents with missing thumbnail', async () => {
      const mockDataWithoutThumbnail = {
        ...mockTemplateData,
        thumbnail: null,
      } as any;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockDataWithoutThumbnail]);
      }

      const result = await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(result.datas[0].thumbnail).toBeNull();
      }
    });

    it('should handle documents with missing department', async () => {
      const mockDataWithoutDepartment = {
        ...mockTemplateData,
        createdBy: {
          id: 1,
          department: null,
        },
      } as any;

      if (!globalThis.dataSource) {
        mockQueryBuilder.getMany.mockResolvedValue([mockDataWithoutDepartment]);
      }

      const result = await repo.getRecentFiles(10);

      if (!globalThis.dataSource) {
        expect(result.datas[0].department).toEqual({
          id: undefined,
          nameTh: undefined,
        });
      }
    });
  });
});

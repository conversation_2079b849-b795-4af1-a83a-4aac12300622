import { Module } from '@nestjs/common';

import { RecentFilesV1HttpController } from './handler/http/recent-files.v1.http.controller';
import { RecentFilesV1Repo } from './recent-files.v1.repo';
import { RecentFilesV1Service } from './recent-files.v1.service';

@Module({
  providers: [RecentFilesV1Repo, RecentFilesV1Service],
  controllers: [RecentFilesV1HttpController],
})
export class RecentFilesV1Module {}

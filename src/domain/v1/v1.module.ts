import { Module } from '@nestjs/common';

import { RolesV1Module } from '@domain/v1/roles/roles.v1.module';

import { AuthsV1Module } from './auths/auths.v1.module';
import { InvitationsV1Module } from './invitations/invitations.v1.module';
import { LayoutsV1Module } from './layouts/laytous.v1.module';
import { OptionsV1Module } from './options/options.v1.module';
import { PermissionsV1Module } from './permissions/permissions.v1.module';
import { RecentFilesV1Module } from './recent-files/recent-files.v1.module';
import { ReportsV1Module } from './reports/reports.v1.module';
import { TemplatesV1Module } from './templates/templates.v1.module';
import { UsersV1Module } from './users/users.v1.module';

@Module({
  imports: [
    UsersV1Module,
    AuthsV1Module,
    RolesV1Module,
    InvitationsV1Module,
    PermissionsV1Module,
    OptionsV1Module,
    TemplatesV1Module,
    ReportsV1Module,
    LayoutsV1Module,
    RecentFilesV1Module,
  ],
})
export class V1Module {}
